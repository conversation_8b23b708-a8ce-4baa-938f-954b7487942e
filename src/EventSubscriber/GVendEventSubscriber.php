<?php

namespace App\EventSubscriber;

use App\Event\GVendCreatedEvent;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class GVendEventSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private LoggerInterface $logger
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            GVendCreatedEvent::NAME => 'onGVendCreated',
        ];
    }

    public function onGVendCreated(GVendCreatedEvent $event): void
    {
        $gvend = $event->getGVend();

        // Your custom logic here
        $this->logger->info('GVend created event received', [
            'id' => $gvend->getId(),
            'imei' => $gvend->getImei(),
            'amount' => $gvend->getAmount()
        ]);

        // Example: Process the transaction
        $this->processVendingTransaction($gvend);
    }

    private function processVendingTransaction($gvend): void
    {
        // Add your business logic here
        // Examples:
        // - Update inventory
        // - Send notifications
        // - Update user balances
        // - Generate reports
        // - Sync with external systems
    }
}
