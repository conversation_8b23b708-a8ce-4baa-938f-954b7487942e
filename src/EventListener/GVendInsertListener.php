<?php

namespace App\EventListener;

use App\Entity\GVend;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\Event\PostPersistEventArgs;
use Doctrine\ORM\Event\PrePersistEventArgs;
use Doctrine\ORM\Events;
use Psr\Log\LoggerInterface;

#[AsDoctrineListener(event: Events::prePersist)]
#[AsDoctrineListener(event: Events::postPersist)]
class GVendInsertListener
{
    public function __construct(
        private LoggerInterface $logger
    ) {
    }

    public function prePersist(PrePersistEventArgs $args): void
    {
        $entity = $args->getObject();

        // Only handle GVend entities
        if (!$entity instanceof GVend) {
            return;
        }

        // Your custom logic before insert
        $this->logger->info('GVend entity about to be inserted', [
            'imei' => $entity->getImei(),
            'amount' => $entity->getAmount(),
            'type' => $entity->getType()
        ]);

        // You can modify the entity here
        // Example: Set default values, validate data, etc.
    }

    public function postPersist(PostPersistEventArgs $args): void
    {
        $entity = $args->getObject();

        // Only handle GVend entities
        if (!$entity instanceof GVend) {
            return;
        }

        // Your custom logic after insert
        $this->logger->info('GVend entity inserted successfully', [
            'id' => $entity->getId(),
            'imei' => $entity->getImei(),
            'amount' => $entity->getAmount()
        ]);

        // Example: Send notifications, update caches, trigger other processes
        $this->handlePostInsert($entity);
    }

    private function handlePostInsert(GVend $gvend): void
    {
        // Add your custom logic here
        // Examples:
        // - Send email notifications
        // - Update statistics
        // - Trigger background jobs
        // - Update caches
        // - Send webhooks
        
        // Example: Log transaction details
        $this->logger->info('Processing new vending transaction', [
            'transaction_id' => $gvend->getId(),
            'machine_imei' => $gvend->getImei(),
            'amount' => $gvend->getAmount(),
            'payment_type' => $gvend->getPaymentType(),
            'timestamp' => $gvend->getTimestamp()?->format('Y-m-d H:i:s')
        ]);
    }
}
