<?php

namespace App\Entity;

use App\Repository\GVendRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * Defines the GVend entity.
 */
#[ORM\Entity(repositoryClass: GVendRepository::class)]
#[ORM\Table(name: 'g_vend')]
#[ORM\Index(name: 'imei_idx', columns: ['imei'])]
#[ORM\Index(name: 'proc_idx', columns: ['proc'])]
#[ORM\HasLifecycleCallbacks]
class GVend
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: Types::INTEGER, options: ['unsigned' => true])]
    private ?int $id = null;

    #[ORM\Column(type: Types::STRING, length: 36)]
    private string $imei;

    /**
     * Vrijeme kad je napravljena transakcija na terenu
     */
    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $timestamp = null;

    /**
     * Vrijeme upisa na server
     */
    #[ORM\Column(
        type: Types::DATETIME_MUTABLE, 
        insertable: false, 
        updatable: false, 
        options: ['default' => 'CURRENT_TIMESTAMP']
    )]
    private \DateTimeInterface $created;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2, options: ['unsigned' => true])]
    private string $amount;

    /**
     * vrsta novca
     */
    #[ORM\Column(name: 'payment_type', type: Types::STRING, length: 36)]
    private string $paymentType;

    /**
     * item no in price holding executive
     */
    #[ORM\Column(type: Types::SMALLINT, options: ['unsigned' => true])]
    private int $item;

    /**
     * 1-kupovanje kave, 2-uplata na stick, 3- token ili reprezentacija, 7-servisni vending
     */
    #[ORM\Column(type: Types::SMALLINT, options: ['unsigned' => true])]
    private int $type;

    /**
     * interni int za borbu protiv duplikata
     */
    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $invid = null;

    /**
     * rfid u slucaju kupovine karticom
     */
    #[ORM\Column(name: 'money_ref', type: Types::STRING, length: 100, nullable: true)]
    private ?string $moneyRef = null;

    /**
     * 1 nakon sto se procesuira redak u fiskalizaciju, prilagoditi Noelu
     */
    #[ORM\Column(type: Types::SMALLINT)]
    private int $stat;

    /**
     * Stanje procesa za prebacivanje u racune prilagoditi Noelu
     */
    #[ORM\Column(type: Types::SMALLINT)]
    private int $proc;


    public function __construct()
    {
        $this->amount = '0.00';
        $this->paymentType = '0';
        $this->item = 0;
        $this->type = 0;
        $this->invid = 0;
        $this->stat = 0;
        $this->proc = 0;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getImei(): string
    {
        return $this->imei;
    }

    public function setImei(string $imei): static
    {
        $this->imei = $imei;
        return $this;
    }

    public function getTimestamp(): ?\DateTimeInterface
    {
        return $this->timestamp;
    }

    public function setTimestamp(?\DateTimeInterface $timestamp): static
    {
        $this->timestamp = $timestamp;
        return $this;
    }

    public function getCreated(): \DateTimeInterface
    {
        return $this->created;
    }

    // No setter for $created as it's generated by the database

    public function getAmount(): string
    {
        return $this->amount;
    }

    public function setAmount(string $amount): static
    {
        $this->amount = $amount;
        return $this;
    }

    public function getPaymentType(): string
    {
        return $this->paymentType;
    }

    public function setPaymentType(string $paymentType): static
    {
        $this->paymentType = $paymentType;
        return $this;
    }

    public function getItem(): int
    {
        return $this->item;
    }

    public function setItem(int $item): static
    {
        $this->item = $item;
        return $this;
    }

    public function getType(): int
    {
        return $this->type;
    }

    public function setType(int $type): static
    {
        $this->type = $type;
        return $this;
    }

    public function getInvid(): ?int
    {
        return $this->invid;
    }

    public function setInvid(?int $invid): static
    {
        $this->invid = $invid;
        return $this;
    }

    public function getMoneyRef(): ?string
    {
        return $this->moneyRef;
    }

    public function setMoneyRef(?string $moneyRef): static
    {
        $this->moneyRef = $moneyRef;
        return $this;
    }

    public function getStat(): int
    {
        return $this->stat;
    }

    public function setStat(int $stat): static
    {
        $this->stat = $stat;
        return $this;
    }

    public function getProc(): int
    {
        return $this->proc;
    }

    public function setProc(int $proc): static
    {
        $this->proc = $proc;
        return $this;
    }

    /**
     * Called automatically before persisting a new entity to the database
     */
    #[ORM\PrePersist]
    public function onPrePersist(): void
    {
        // This runs BEFORE the entity is inserted into the database
        // You can modify the entity here before it's saved

        // Example: Set a default timestamp if not already set
        if ($this->timestamp === null) {
            $this->timestamp = new \DateTime();
        }

        // Add your custom logic here
        $this->handleNewRecord();
    }

    /**
     * Called automatically after persisting a new entity to the database
     */
    #[ORM\PostPersist]
    public function onPostPersist(): void
    {
        // This runs AFTER the entity is inserted into the database
        // The entity now has an ID and is fully persisted

        // Add your custom logic here
        $this->handleAfterInsert();
    }

    /**
     * Custom logic to run when a new record is being created
     */
    private function handleNewRecord(): void
    {
        // Add your custom logic here
        // This runs before the record is saved to the database

        // Example: Log the new vending transaction
        error_log("New vending transaction being created for IMEI: " . $this->imei);

        // Example: Set default values or validate data
        if (empty($this->paymentType)) {
            $this->paymentType = 'cash';
        }
    }

    /**
     * Custom logic to run after a new record is inserted
     */
    private function handleAfterInsert(): void
    {
        // Add your custom logic here
        // This runs after the record is saved to the database

        // Example: Send notifications, update caches, trigger other processes
        error_log("New vending transaction saved with ID: " . $this->id);

        // You could trigger events, send emails, update statistics, etc.
    }
}